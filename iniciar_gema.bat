@echo off
echo ========================================
echo    INICIANDO AGENTE GEMA
echo ========================================
echo.

REM Tenta diferentes formas de executar Python
echo Tentando executar com 'py'...
py conversa_direta.py
if %errorlevel% neq 0 (
    echo.
    echo Tentando executar com 'python'...
    python conversa_direta.py
    if %errorlevel% neq 0 (
        echo.
        echo Tentando executar com 'python3'...
        python3 conversa_direta.py
        if %errorlevel% neq 0 (
            echo.
            echo ERRO: Nao foi possivel executar Python
            echo Verifique se o Python esta instalado e nas variaveis de ambiente
            echo.
            pause
        )
    )
)

pause
