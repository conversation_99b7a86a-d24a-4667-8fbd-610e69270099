import sys
import time
import json
import socket
import threading
from datetime import datetime

class AgenteGema:
    def __init__(self):
        self.nome = "Gema"
        self.ponte_ativa = False
        self.servidor_socket = None
        self.conexoes_ativas = []
        
    def responder(self, mensagem):
        # Simula o processo de "pensamento" do agente
        print("Processando...", end="")
        for _ in range(3):
            time.sleep(0.5)
            print(".", end="", flush=True)
        print("\n")
        
        mensagem = mensagem.lower()
        
        # Respostas básicas do agente
        if "olá" in mensagem or "oi" in mensagem:
            return f"Olá! Eu sou a {self.nome}. Como posso ajudar você hoje?"
        elif "como você está" in mensagem:
            return "Estou funcionando perfeitamente, obrigada por perguntar!"
        elif "ajuda" in mensagem:
            return "Posso ajudar você com várias coisas! Você pode me perguntar sobre:\n- Como estou\n- Quem sou eu\n- E muito mais!"
        elif "tchau" in mensagem:
            return "Até logo! Foi um prazer conversar com você!"
        elif "ponte" in mensagem and "ativar" in mensagem:
            return self.ativar_ponte()
        elif "ponte" in mensagem and "desativar" in mensagem:
            return self.desativar_ponte()
        elif "status" in mensagem and "ponte" in mensagem:
            return self.status_ponte()
        else:
            return "Desculpe, ainda estou aprendendo. Pode reformular sua pergunta?"

    def ativar_ponte(self):
        """Ativa a ponte de comunicação com outros agentes"""
        if self.ponte_ativa:
            return "A ponte de comunicação já está ativa!"

        try:
            self.servidor_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.servidor_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.servidor_socket.bind(('localhost', 8888))
            self.servidor_socket.listen(5)

            # Inicia thread para aceitar conexões
            thread_servidor = threading.Thread(target=self._aceitar_conexoes)
            thread_servidor.daemon = True
            thread_servidor.start()

            self.ponte_ativa = True
            return "🌉 Ponte de comunicação ativada! Aguardando conexões na porta 8888..."
        except Exception as e:
            return f"Erro ao ativar ponte: {e}"

    def desativar_ponte(self):
        """Desativa a ponte de comunicação"""
        if not self.ponte_ativa:
            return "A ponte de comunicação já está desativada!"

        try:
            # Fecha todas as conexões ativas
            for conn in self.conexoes_ativas:
                conn.close()
            self.conexoes_ativas.clear()

            # Fecha o servidor
            if self.servidor_socket:
                self.servidor_socket.close()

            self.ponte_ativa = False
            return "🌉 Ponte de comunicação desativada!"
        except Exception as e:
            return f"Erro ao desativar ponte: {e}"

    def status_ponte(self):
        """Retorna o status da ponte de comunicação"""
        if self.ponte_ativa:
            return f"🌉 Ponte ATIVA - {len(self.conexoes_ativas)} conexões ativas"
        else:
            return "🌉 Ponte INATIVA"

    def _aceitar_conexoes(self):
        """Thread para aceitar novas conexões"""
        while self.ponte_ativa:
            try:
                conn, addr = self.servidor_socket.accept()
                self.conexoes_ativas.append(conn)
                print(f"\n🔗 Nova conexão estabelecida com {addr}")

                # Inicia thread para gerenciar esta conexão
                thread_conexao = threading.Thread(target=self._gerenciar_conexao, args=(conn, addr))
                thread_conexao.daemon = True
                thread_conexao.start()

            except Exception as e:
                if self.ponte_ativa:  # Só mostra erro se a ponte ainda deveria estar ativa
                    print(f"Erro ao aceitar conexão: {e}")
                break

    def _gerenciar_conexao(self, conn, addr):
        """Gerencia uma conexão específica"""
        try:
            # Envia mensagem de boas-vindas
            mensagem_boas_vindas = {
                "tipo": "boas_vindas",
                "agente": self.nome,
                "timestamp": datetime.now().isoformat(),
                "mensagem": f"Olá! Eu sou a {self.nome}. Conexão estabelecida com sucesso!"
            }
            self._enviar_mensagem(conn, mensagem_boas_vindas)

            while self.ponte_ativa:
                # Recebe mensagem
                data = conn.recv(1024).decode('utf-8')
                if not data:
                    break

                try:
                    mensagem_recebida = json.loads(data)
                    print(f"\n📨 Mensagem recebida de {addr}: {mensagem_recebida.get('conteudo', '')}")

                    # Processa a mensagem
                    resposta_conteudo = self.responder(mensagem_recebida.get('conteudo', ''))

                    # Envia resposta
                    resposta = {
                        "tipo": "resposta",
                        "agente": self.nome,
                        "timestamp": datetime.now().isoformat(),
                        "mensagem": resposta_conteudo,
                        "em_resposta_a": mensagem_recebida.get('id', '')
                    }
                    self._enviar_mensagem(conn, resposta)

                except json.JSONDecodeError:
                    # Se não for JSON, trata como mensagem simples
                    print(f"\n📨 Mensagem simples de {addr}: {data}")
                    resposta_conteudo = self.responder(data)

                    resposta = {
                        "tipo": "resposta",
                        "agente": self.nome,
                        "timestamp": datetime.now().isoformat(),
                        "mensagem": resposta_conteudo
                    }
                    self._enviar_mensagem(conn, resposta)

        except Exception as e:
            print(f"Erro na conexão com {addr}: {e}")
        finally:
            if conn in self.conexoes_ativas:
                self.conexoes_ativas.remove(conn)
            conn.close()
            print(f"🔌 Conexão com {addr} encerrada")

    def _enviar_mensagem(self, conn, mensagem):
        """Envia mensagem JSON para uma conexão"""
        try:
            mensagem_json = json.dumps(mensagem, ensure_ascii=False)
            conn.send(mensagem_json.encode('utf-8'))
        except Exception as e:
            print(f"Erro ao enviar mensagem: {e}")

def main():
    agente = AgenteGema()
    print(f"=== Agente {agente.nome} iniciado ===")
    print("Digite 'tchau' para sair")
    print("Comandos especiais:")
    print("  - 'ativar ponte' - Ativa a ponte de comunicação")
    print("  - 'desativar ponte' - Desativa a ponte de comunicação")
    print("  - 'status ponte' - Verifica status da ponte")
    
    while True:
        try:
            usuario_input = input("\nVocê: ")
            if usuario_input.lower() == "tchau":
                print(agente.responder(usuario_input))
                break
            
            resposta = agente.responder(usuario_input)
            print(f"\n{agente.nome}: {resposta}")
            
        except KeyboardInterrupt:
            print("\nEncerrando o programa...")
            break
        except Exception as e:
            print(f"\nOcorreu um erro: {e}")
            break

if __name__ == "__main__":
    main()
