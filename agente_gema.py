import sys
import time

class AgenteGema:
    def __init__(self):
        self.nome = "Gema"
        
    def responder(self, mensagem):
        # Simula o processo de "pensamento" do agente
        print("Processando...", end="")
        for _ in range(3):
            time.sleep(0.5)
            print(".", end="", flush=True)
        print("\n")
        
        mensagem = mensagem.lower()
        
        # Respostas básicas do agente
        if "olá" in mensagem or "oi" in mensagem:
            return f"Olá! Eu sou a {self.nome}. Como posso ajudar você hoje?"
        elif "como você está" in mensagem:
            return "Estou funcionando perfeitamente, obrigada por perguntar!"
        elif "ajuda" in mensagem:
            return "Posso ajudar você com várias coisas! Você pode me perguntar sobre:\n- Como estou\n- Quem sou eu\n- E muito mais!"
        elif "tchau" in mensagem:
            return "Até logo! Foi um prazer conversar com você!"
        else:
            return "Desculpe, ainda estou aprendendo. Pode reformular sua pergunta?"

def main():
    agente = AgenteGema()
    print(f"=== Agente {agente.nome} iniciado ===")
    print("Digite 'tchau' para sair")
    
    while True:
        try:
            usuario_input = input("\nVocê: ")
            if usuario_input.lower() == "tchau":
                print(agente.responder(usuario_input))
                break
            
            resposta = agente.responder(usuario_input)
            print(f"\n{agente.nome}: {resposta}")
            
        except KeyboardInterrupt:
            print("\nEncerrando o programa...")
            break
        except Exception as e:
            print(f"\nOcorreu um erro: {e}")
            break

if __name__ == "__main__":
    main()
