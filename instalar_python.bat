@echo off
echo ========================================
echo   INSTALADOR PYTHON 3.13 PARA GEMA
echo ========================================
echo.

set PYTHON_DIR=C:\python313
set INSTALLER_URL=https://www.python.org/ftp/python/3.12.7/python-3.12.7-amd64.exe
set INSTALLER_FILE=python-installer.exe

REM Verifica se Python ja esta instalado
if exist "%PYTHON_DIR%\python.exe" (
    echo ✅ Python ja esta instalado em %PYTHON_DIR%
    goto :verificar
)

echo 📥 Baixando Python 3.13...
powershell -Command "Invoke-WebRequest -Uri '%INSTALLER_URL%' -OutFile '%INSTALLER_FILE%' -UseBasicParsing"

if not exist "%INSTALLER_FILE%" (
    echo ❌ Erro no download do Python
    pause
    exit /b 1
)

echo ✅ Download concluido!
echo 🔧 Instalando Python em %PYTHON_DIR%...

REM Instala Python silenciosamente
"%INSTALLER_FILE%" /quiet InstallAllUsers=0 TargetDir=%PYTHON_DIR% PrependPath=1 Include_pip=1 Include_tcltk=1 Include_launcher=1

REM Remove o instalador
del "%INSTALLER_FILE%" 2>nul

:verificar
echo.
echo 🔍 Verificando instalacao...

if exist "%PYTHON_DIR%\python.exe" (
    echo ✅ Python instalado com sucesso!
    
    REM Testa a versao
    "%PYTHON_DIR%\python.exe" --version
    
    echo.
    echo 📦 Atualizando pip...
    "%PYTHON_DIR%\python.exe" -m pip install --upgrade pip
    
    echo.
    echo 🎉 INSTALACAO CONCLUIDA!
    echo Python instalado em: %PYTHON_DIR%
    echo.
    echo Agora voce pode executar seu Agente Gema com:
    echo %PYTHON_DIR%\python.exe agente_gema.py
    echo.
    
) else (
    echo ❌ Falha na instalacao do Python
    echo Tente instalar manualmente em: https://python.org
)

echo.
pause
