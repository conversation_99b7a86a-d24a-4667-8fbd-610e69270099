@echo off
echo ========================================
echo    REPARACAO TOTAL DO WINDOWS
echo ========================================
echo.
echo ⚠️  AVISO IMPORTANTE:
echo.
echo Este script ira executar uma reparacao completa:
echo - Verificar e reparar TrustedInstaller
echo - Reparar Windows Explorer
echo - Reparar CMD e PowerShell
echo - Verificar assinaturas da Microsoft
echo - Executar SFC e DISM
echo - Limpar rootkits e malware
echo.
echo ⏰ Tempo estimado: 30-60 minutos
echo.
echo Certifique-se de:
echo 1. Executar como ADMINISTRADOR
echo 2. Ter conexao com internet
echo 3. Nao usar o computador durante o processo
echo.
echo Pressione qualquer tecla para continuar ou Ctrl+C para cancelar...
pause >nul

REM Verifica se esta executando como administrador
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ ERRO CRITICO: Este script DEVE ser executado como ADMINISTRADOR
    echo.
    echo Como executar como administrador:
    echo 1. Clique com botao direito no arquivo
    echo 2. Selecione "Executar como administrador"
    echo 3. Clique "Sim" na janela do UAC
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Executando como administrador - Iniciando reparacao...
echo.

echo ========================================
echo FASE 1: REPARACAO SISTEMA COMPLETO
echo ========================================
echo.
call reparar_sistema_completo.bat

echo.
echo ========================================
echo FASE 2: REPARACAO ESPECIFICA DO EXPLORER
echo ========================================
echo.
call reparar_explorer.bat

echo.
echo ========================================
echo FASE 3: VERIFICACAO DE ASSINATURAS
echo ========================================
echo.
call verificar_assinaturas.bat

echo.
echo ========================================
echo FASE 4: LIMPEZA FINAL E OTIMIZACAO
echo ========================================
echo.

echo 🧹 Executando limpeza de disco...
cleanmgr /sagerun:1

echo.
echo 🔍 Verificacao final do sistema...
sfc /verifyonly

echo.
echo ========================================
echo    REPARACAO TOTAL CONCLUIDA!
echo ========================================
echo.
echo ✅ PROCESSOS EXECUTADOS:
echo   - TrustedInstaller reparado
echo   - Windows Explorer reparado  
echo   - CMD e PowerShell reparados
echo   - Assinaturas verificadas
echo   - Sistema limpo e otimizado
echo.
echo 🔄 PROXIMOS PASSOS OBRIGATORIOS:
echo   1. REINICIE O COMPUTADOR AGORA
echo   2. Execute Windows Update
echo   3. Execute verificacao completa do antivirus
echo   4. Monitore o sistema por alguns dias
echo.
echo ⚠️  Se ainda houver problemas apos reiniciar:
echo   - Considere fazer backup dos dados
echo   - Execute uma instalacao limpa do Windows
echo.
echo Pressione qualquer tecla para finalizar...
pause >nul

echo.
echo 🔄 Deseja reiniciar o computador agora? (S/N)
set /p resposta=
if /i "%resposta%"=="S" (
    echo Reiniciando em 10 segundos...
    shutdown /r /t 10
) else (
    echo Lembre-se de reiniciar o computador manualmente!
)
