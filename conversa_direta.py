#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from agente_gema import AgenteGema
import sys

def conversa_direta():
    """Conversa direta com o Agente Gema no terminal"""

    # Cria instância do agente
    gema = AgenteGema()

    print("🤖 Conexão direta estabelecida com Agente Gema!", flush=True)
    print(f"=== {gema.nome} está online ===", flush=True)
    print("Digite suas mensagens abaixo (ou 'sair' para encerrar)", flush=True)
    print("-" * 50, flush=True)

    # Mensagem inicial do agente
    print(f"\n🤖 {gema.nome}: Olá! Eu sou a Gema. Como posso ajudar você hoje?", flush=True)
    
    while True:
        try:
            # Recebe input do usuário
            mensagem = input("\n💬 Você: ")

            # Verifica se quer sair
            if mensagem.lower() in ['sair', 'exit', 'quit', 'tchau']:
                resposta = gema.responder("tchau")
                print(f"\n🤖 {gema.nome}: {resposta}", flush=True)
                break

            # Processa a mensagem
            if mensagem.strip():
                resposta = gema.responder(mensagem)
                print(f"\n🤖 {gema.nome}: {resposta}", flush=True)
            
        except KeyboardInterrupt:
            print(f"\n\n🤖 {gema.nome}: Até logo! Foi um prazer conversar com você!", flush=True)
            break
        except Exception as e:
            print(f"\n❌ Erro: {e}", flush=True)
            break

    print("\n👋 Conexão encerrada!", flush=True)

if __name__ == "__main__":
    conversa_direta()
