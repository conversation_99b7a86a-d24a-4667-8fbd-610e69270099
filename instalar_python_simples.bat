@echo off
echo ========================================
echo     INSTALADOR PYTHON PARA GEMA
echo ========================================
echo.

REM Define o diretorio de instalacao
set PYTHON_DIR=C:\python313

echo 📥 Baixando Python 3.12.7...
echo Aguarde, isso pode demorar alguns minutos...
echo.

REM Baixa o Python
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.12.7/python-3.12.7-amd64.exe' -OutFile 'python_installer.exe'}"

if not exist "python_installer.exe" (
    echo ❌ Erro: Nao foi possivel baixar o Python
    echo Verifique sua conexao com a internet
    pause
    exit /b 1
)

echo ✅ Download concluido!
echo.
echo 🔧 Instalando Python em %PYTHON_DIR%...
echo Esta etapa pode demorar alguns minutos...
echo.

REM Instala o Python
python_installer.exe /quiet InstallAllUsers=0 TargetDir=%PYTHON_DIR% PrependPath=1 Include_pip=1

REM Aguarda a instalacao terminar
timeout /t 30 /nobreak >nul

REM Verifica se foi instalado
if exist "%PYTHON_DIR%\python.exe" (
    echo ✅ Python instalado com sucesso em %PYTHON_DIR%!
    echo.
    
    REM Testa a versao
    echo 🔍 Verificando versao...
    "%PYTHON_DIR%\python.exe" --version
    echo.
    
    echo 🎉 INSTALACAO DO PYTHON CONCLUIDA!
    echo.
    echo Proximo passo: Execute instalar_bibliotecas.bat
    echo.
    
) else (
    echo ❌ Erro na instalacao do Python
    echo Tente executar python_installer.exe manualmente
)

REM Remove o instalador
del python_installer.exe 2>nul

echo.
pause
