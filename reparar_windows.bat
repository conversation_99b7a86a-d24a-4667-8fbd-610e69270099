@echo off
echo ========================================
echo   REPARACAO COMPLETA DO WINDOWS
echo ========================================
echo.
echo AVISO: Este script ira:
echo - Verificar integridade dos arquivos do sistema
echo - Reparar TrustedInstaller
echo - Restaurar CMD original
echo - Atualizar assinaturas da Microsoft
echo - Executar verificacoes de seguranca
echo.
echo Certifique-se de executar como ADMINISTRADOR
echo.
pause

REM Verifica se esta executando como administrador
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERRO: Execute este script como ADMINISTRADOR
    echo Clique com botao direito e selecione "Executar como administrador"
    pause
    exit /b 1
)

echo ✅ Executando como administrador
echo.

echo ========================================
echo ETAPA 1: PARANDO SERVICOS SUSPEITOS
echo ========================================
echo.

REM Para servicos que podem estar comprometidos
net stop TrustedInstaller 2>nul
net stop wuauserv 2>nul
net stop cryptsvc 2>nul
net stop bits 2>nul
net stop msiserver 2>nul

echo ✅ Servicos parados
echo.

echo ========================================
echo ETAPA 2: VERIFICACAO SFC (System File Checker)
echo ========================================
echo.

echo 🔍 Executando SFC /scannow...
sfc /scannow

echo.
echo ========================================
echo ETAPA 3: VERIFICACAO DISM
echo ========================================
echo.

echo 🔍 Verificando integridade da imagem do Windows...
DISM /Online /Cleanup-Image /CheckHealth

echo.
echo 🔧 Reparando imagem do Windows...
DISM /Online /Cleanup-Image /RestoreHealth

echo.
echo ========================================
echo ETAPA 4: RESET DO WINDOWS UPDATE
echo ========================================
echo.

echo 🔄 Resetando componentes do Windows Update...

REM Para o Windows Update
net stop wuauserv
net stop cryptSvc
net stop bits
net stop msiserver

REM Renomeia pastas do Windows Update
ren C:\Windows\SoftwareDistribution SoftwareDistribution.old
ren C:\Windows\System32\catroot2 catroot2.old

REM Reinicia servicos
net start wuauserv
net start cryptSvc
net start bits
net start msiserver

echo ✅ Windows Update resetado
echo.

echo ========================================
echo ETAPA 5: VERIFICACAO DE MALWARE
echo ========================================
echo.

echo 🛡️ Executando Windows Defender...
"%ProgramFiles%\Windows Defender\MpCmdRun.exe" -Scan -ScanType 2

echo.
echo ========================================
echo ETAPA 6: ATUALIZACAO DE ASSINATURAS
echo ========================================
echo.

echo 📝 Atualizando assinaturas da Microsoft...
"%ProgramFiles%\Windows Defender\MpCmdRun.exe" -SignatureUpdate

echo.
echo ========================================
echo ETAPA 7: VERIFICACAO FINAL
echo ========================================
echo.

echo 🔍 Verificacao final do sistema...
sfc /verifyonly

echo.
echo 🎉 REPARACAO CONCLUIDA!
echo.
echo Recomendacoes:
echo 1. Reinicie o computador
echo 2. Execute Windows Update
echo 3. Execute uma verificacao completa do antivirus
echo.
pause
