@echo off
echo ========================================
echo   REPARACAO COMPLETA DO SISTEMA
echo ========================================
echo.
echo Este script ira reparar:
echo - TrustedInstaller
echo - Windows Explorer  
echo - CMD (Prompt de Comando)
echo - Componentes do sistema
echo - Assinaturas da Microsoft
echo - Servicos do Windows
echo.
echo ⚠️  IMPORTANTE: Execute como ADMINISTRADOR
echo.
pause

REM Verifica se esta executando como administrador
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERRO: Execute este script como ADMINISTRADOR
    echo Clique com botao direito e "Executar como administrador"
    pause
    exit /b 1
)

echo ✅ Executando como administrador
echo.

echo ========================================
echo ETAPA 1: PARANDO PROCESSOS E SERVICOS
echo ========================================
echo.

REM Para processos potencialmente comprometidos
taskkill /f /im explorer.exe 2>nul
net stop TrustedInstaller 2>nul
net stop wuauserv 2>nul
net stop cryptsvc 2>nul
net stop bits 2>nul

echo ✅ Processos parados
echo.

echo ========================================
echo ETAPA 2: REPARANDO TRUSTEDINSTALLER
echo ========================================
echo.

echo 🔧 Reparando TrustedInstaller...
regsvr32 /s %windir%\system32\wuapi.dll
regsvr32 /s %windir%\system32\wuaueng.dll
regsvr32 /s %windir%\system32\wups.dll
regsvr32 /s %windir%\system32\wups2.dll

sc config TrustedInstaller start= demand
net start TrustedInstaller

echo ✅ TrustedInstaller reparado
echo.

echo ========================================
echo ETAPA 3: REPARANDO CMD E POWERSHELL
echo ========================================
echo.

echo 🔧 Reparando CMD...
REM Restaura associacoes do CMD
assoc .bat=batfile
assoc .cmd=cmdfile
ftype batfile="%1" %*
ftype cmdfile="%1" %*

REM Registra componentes do CMD
regsvr32 /s cmd.exe 2>nul

echo ✅ CMD reparado
echo.

echo ========================================
echo ETAPA 4: REPARANDO WINDOWS EXPLORER
echo ========================================
echo.

echo 🔧 Reparando Explorer...
regsvr32 /s shell32.dll
regsvr32 /s ole32.dll
regsvr32 /s oleaut32.dll
regsvr32 /s browseui.dll
regsvr32 /s shdocvw.dll

REM Repara registro do Explorer
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" /v Shell /t REG_SZ /d "explorer.exe" /f

echo ✅ Explorer reparado
echo.

echo ========================================
echo ETAPA 5: VERIFICACAO SFC E DISM
echo ========================================
echo.

echo 🔍 Executando SFC /scannow...
sfc /scannow

echo.
echo 🔍 Executando DISM...
DISM /Online /Cleanup-Image /RestoreHealth

echo.
echo ========================================
echo ETAPA 6: ATUALIZANDO ASSINATURAS
echo ========================================
echo.

echo 📝 Atualizando definicoes do Windows Defender...
"%ProgramFiles%\Windows Defender\MpCmdRun.exe" -SignatureUpdate 2>nul

echo.
echo ========================================
echo ETAPA 7: REINICIANDO SERVICOS
echo ========================================
echo.

echo 🚀 Reiniciando servicos essenciais...
net start wuauserv
net start cryptsvc  
net start bits
net start TrustedInstaller

echo.
echo 🚀 Reiniciando Windows Explorer...
start explorer.exe

echo.
echo ========================================
echo ETAPA 8: VERIFICACAO FINAL
echo ========================================
echo.

echo 🔍 Verificando servicos...
sc query TrustedInstaller | find "RUNNING" >nul
if %errorlevel% equ 0 (
    echo ✅ TrustedInstaller: OK
) else (
    echo ❌ TrustedInstaller: ERRO
)

tasklist /fi "imagename eq explorer.exe" | find /i "explorer.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ Windows Explorer: OK
) else (
    echo ❌ Windows Explorer: ERRO
)

echo.
echo 🎉 REPARACAO COMPLETA FINALIZADA!
echo.
echo PROXIMOS PASSOS RECOMENDADOS:
echo 1. Reinicie o computador
echo 2. Execute Windows Update
echo 3. Execute verificacao completa do antivirus
echo 4. Execute: chkdsk C: /f /r (no proximo reinicio)
echo.
echo Pressione qualquer tecla para finalizar...
pause >nul
