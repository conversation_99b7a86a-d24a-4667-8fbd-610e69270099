# Agente Gema

Um agente de conversação inteligente implementado em Python com capacidades de comunicação avançadas.

## Funcionalidades

- Responde a saudações básicas
- Fornece informações sobre si mesmo
- Interage de forma amigável com o usuário
- Sistema de resposta simulando "pensamento"
- **🌉 Ponte de Comunicação**: Permite comunicação com outros agentes via socket
- **🤖 Comunicação com IA**: Interface para conversar com agentes externos

## Como usar

### Modo Básico
1. Certifique-se de ter Python instalado em seu sistema
2. Execute o arquivo `agente_gema.py`
3. Interaja com o agente digitando mensagens
4. Digite 'tchau' para encerrar a conversa

### Modo Ponte de Comunicação
1. Execute o agente: `python agente_gema.py`
2. Digite `ativar ponte` para ativar a comunicação externa
3. Em outro terminal, execute: `python conversa_automatica.py`
4. <PERSON><PERSON> as instruções para estabelecer comunicação

### Comandos Especiais
- `ativar ponte` - Ativa a ponte de comunicação (porta 8888)
- `desativar ponte` - Desativa a ponte de comunicação
- `status ponte` - Verifica o status da ponte

## Arquivos do Sistema

- `agente_gema.py` - Agente principal com ponte de comunicação
- `ponte_comunicacao.py` - Cliente para conectar com o agente
- `conversa_automatica.py` - Script automatizado para comunicação

## Requisitos

- Python 3.6 ou superior
- Bibliotecas padrão: socket, json, threading, datetime

## Exemplo de uso

### Uso Básico
```
=== Agente Gema iniciado ===
Digite 'tchau' para sair
Comandos especiais:
  - 'ativar ponte' - Ativa a ponte de comunicação
  - 'desativar ponte' - Desativa a ponte de comunicação
  - 'status ponte' - Verifica status da ponte

Você: olá
Processando...

Gema: Olá! Eu sou a Gema. Como posso ajudar você hoje?
```

### Exemplo com Ponte de Comunicação
```
Você: ativar ponte
Processando...

Gema: 🌉 Ponte de comunicação ativada! Aguardando conexões na porta 8888...

🔗 Nova conexão estabelecida com ('127.0.0.1', 54321)
📨 Mensagem recebida de ('127.0.0.1', 54321): Olá Gema! Sou o Agente Augment AI
```

## Comunicação entre Agentes

A ponte de comunicação permite que outros agentes se conectem e conversem com a Gema através de sockets TCP. As mensagens são trocadas em formato JSON com timestamps e identificação dos agentes.
