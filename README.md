# Agente Gema

Um agente de conversação simples implementado em Python.

## Funcionalidades

- Responde a saudações básicas
- Fornece informações sobre si mesmo
- Interage de forma amigável com o usuário
- Sistema de resposta simulando "pensamento"

## Como usar

1. Certifique-se de ter Python instalado em seu sistema
2. Execute o arquivo `agente_gema.py`
3. Interaja com o agente digitando mensagens
4. Digite 'tchau' para encerrar a conversa

## Requisitos

- Python 3.6 ou superior

## Exemplo de uso

```
=== Agente Gema iniciado ===
Digite 'tchau' para sair

Você: olá
Processando...

Gema: Olá! Eu sou a Gema. Como posso ajudar você hoje?
```
