# Script PowerShell para executar o Agente Gema
Write-Host "🤖 INICIANDO AGENTE GEMA" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# Tenta encontrar Python
$pythonCommands = @("python", "python3", "py")
$pythonFound = $false

foreach ($cmd in $pythonCommands) {
    try {
        & $cmd --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Python encontrado: $cmd" -ForegroundColor Green
            
            # Executa o agente Gema
            Write-Host "🚀 Executando Agente Gema..." -ForegroundColor Yellow
            & $cmd agente_gema.py
            $pythonFound = $true
            break
        }
    }
    catch {
        continue
    }
}

if (-not $pythonFound) {
    Write-Host "❌ Python não encontrado!" -ForegroundColor Red
    Write-Host "Instale Python em: https://python.org" -ForegroundColor Yellow
    Read-Host "Pressione Enter para sair"
}
