<!-- Use this file to provide workspace-specific custom instructions to Copilot. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# Agente Gema - Instruções para Copilot

Este é um projeto de um agente de conversação em Python. O agente deve:

- Manter um tom amigável e prestativo
- Fornecer respostas claras e concisas
- Implementar tratamento de erros adequado
- Seguir boas práticas de programação Python
- Manter o código organizado e bem documentado
