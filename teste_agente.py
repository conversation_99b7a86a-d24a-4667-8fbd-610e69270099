#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("Iniciando teste do agente...")

try:
    from agente_gema import AgenteGema
    print("✅ Importação bem-sucedida!")
    
    agente = AgenteGema()
    print(f"✅ Agente {agente.nome} criado com sucesso!")
    
    # Teste básico
    resposta = agente.responder("olá")
    print(f"✅ Teste de resposta: {resposta}")
    
    print("\n🎉 Agente funcionando perfeitamente!")
    print("Agora você pode executar: py agente_gema.py")
    
except Exception as e:
    print(f"❌ Erro: {e}")
    import traceback
    traceback.print_exc()
