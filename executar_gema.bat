@echo off
echo ========================================
echo      EXECUTANDO AGENTE GEMA
echo ========================================
echo.

REM Define o caminho do Python
set PYTHON_DIR=C:\python313
set PYTHON_EXE=%PYTHON_DIR%\python.exe

REM Verifica se Python esta instalado
if not exist "%PYTHON_EXE%" (
    echo ❌ Python nao encontrado em %PYTHON_DIR%
    echo.
    echo Execute primeiro:
    echo 1. instalar_python_simples.bat
    echo 2. instalar_bibliotecas.bat
    echo.
    pause
    exit /b 1
)

REM Verifica se o arquivo do agente existe
if not exist "agente_gema.py" (
    echo ❌ Arquivo agente_gema.py nao encontrado
    echo Certifique-se de estar no diretorio correto
    pause
    exit /b 1
)

echo ✅ Python encontrado: %PYTHON_EXE%
echo ✅ Agente Gema encontrado: agente_gema.py
echo.
echo 🚀 Iniciando Agente Gema...
echo.
echo ========================================
echo.

REM Executa o Agente Gema
"%PYTHON_EXE%" agente_gema.py

echo.
echo ========================================
echo 👋 Agente Gema encerrado
echo ========================================
pause
