# -*- coding: utf-8 -*-
import time

class GemaSimples:
    def __init__(self):
        self.nome = "Gema"
        
    def responder(self, mensagem):
        # Simula o processo de "pensamento" do agente
        print("Processando", end="", flush=True)
        for _ in range(3):
            time.sleep(0.3)
            print(".", end="", flush=True)
        print()
        
        mensagem = mensagem.lower()
        
        # Respostas básicas do agente
        if "olá" in mensagem or "oi" in mensagem:
            return f"Olá! Eu sou a {self.nome}. Como posso ajudar você hoje?"
        elif "como você está" in mensagem:
            return "Estou funcionando perfeitamente, obrigada por perguntar!"
        elif "ajuda" in mensagem:
            return "Posso ajudar você com várias coisas! Você pode me perguntar sobre:\n- Como estou\n- Quem sou eu\n- E muito mais!"
        elif "tchau" in mensagem or "sair" in mensagem:
            return "Até logo! Foi um prazer conversar com você!"
        elif "quem" in mensagem and "você" in mensagem:
            return f"Eu sou a {self.nome}, um agente de conversação criado para ajudar e conversar!"
        elif "nome" in mensagem:
            return f"Meu nome é {self.nome}!"
        else:
            return "Desculpe, ainda estou aprendendo. Pode reformular sua pergunta?"

def main():
    print("=" * 60)
    print("🤖 AGENTE GEMA - VERSÃO SIMPLES")
    print("=" * 60)
    print()
    
    gema = GemaSimples()
    print(f"✅ {gema.nome} está online e pronta para conversar!")
    print("💬 Digite suas mensagens abaixo")
    print("🚪 Digite 'sair' ou 'tchau' para encerrar")
    print("-" * 60)
    
    # Mensagem inicial
    print(f"\n🤖 {gema.nome}: Olá! Eu sou a Gema. Como posso ajudar você hoje?")
    
    while True:
        try:
            # Recebe input do usuário
            mensagem = input(f"\n💬 Você: ")
            
            # Verifica se quer sair
            if mensagem.lower() in ['sair', 'exit', 'quit', 'tchau']:
                resposta = gema.responder("tchau")
                print(f"\n🤖 {gema.nome}: {resposta}")
                break
            
            # Processa a mensagem
            if mensagem.strip():
                resposta = gema.responder(mensagem)
                print(f"\n🤖 {gema.nome}: {resposta}")
            
        except KeyboardInterrupt:
            print(f"\n\n🤖 {gema.nome}: Até logo! Foi um prazer conversar com você!")
            break
        except EOFError:
            print(f"\n\n🤖 {gema.nome}: Até logo! Foi um prazer conversar com você!")
            break
        except Exception as e:
            print(f"\n❌ Erro: {e}")
            break
    
    print("\n👋 Conversa encerrada!")
    print("Obrigado por usar o Agente Gema!")
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
