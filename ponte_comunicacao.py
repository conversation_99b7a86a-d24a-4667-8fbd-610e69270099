import socket
import json
import threading
import time
from datetime import datetime

class PonteComunicacao:
    def __init__(self, nome_agente="Agente Augment"):
        self.nome = nome_agente
        self.socket_cliente = None
        self.conectado = False
        self.thread_escuta = None
        
    def conectar(self, host='localhost', porta=8888):
        """Conecta ao agente Gema"""
        try:
            self.socket_cliente = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket_cliente.connect((host, porta))
            self.conectado = True
            
            # Inicia thread para escutar mensagens
            self.thread_escuta = threading.Thread(target=self._escutar_mensagens)
            self.thread_escuta.daemon = True
            self.thread_escuta.start()
            
            print(f"🌉 Conectado ao agente Gema em {host}:{porta}")
            return True
            
        except Exception as e:
            print(f"❌ Erro ao conectar: {e}")
            return False
    
    def desconectar(self):
        """Desconecta do agente Gema"""
        self.conectado = False
        if self.socket_cliente:
            self.socket_cliente.close()
        print("🔌 Desconectado do agente Gema")
    
    def enviar_mensagem(self, conteudo):
        """Envia mensagem para o agente Gema"""
        if not self.conectado:
            print("❌ Não conectado ao agente Gema")
            return False
        
        try:
            mensagem = {
                "tipo": "mensagem",
                "agente": self.nome,
                "timestamp": datetime.now().isoformat(),
                "conteudo": conteudo,
                "id": f"{self.nome}_{int(time.time())}"
            }
            
            mensagem_json = json.dumps(mensagem, ensure_ascii=False)
            self.socket_cliente.send(mensagem_json.encode('utf-8'))
            print(f"📤 Enviado: {conteudo}")
            return True
            
        except Exception as e:
            print(f"❌ Erro ao enviar mensagem: {e}")
            return False
    
    def _escutar_mensagens(self):
        """Thread para escutar mensagens do agente Gema"""
        while self.conectado:
            try:
                data = self.socket_cliente.recv(1024).decode('utf-8')
                if not data:
                    break
                
                try:
                    mensagem = json.loads(data)
                    timestamp = mensagem.get('timestamp', '')
                    agente = mensagem.get('agente', 'Desconhecido')
                    conteudo = mensagem.get('mensagem', data)
                    
                    print(f"\n📥 {agente}: {conteudo}")
                    
                except json.JSONDecodeError:
                    print(f"\n📥 Gema: {data}")
                    
            except Exception as e:
                if self.conectado:
                    print(f"❌ Erro ao receber mensagem: {e}")
                break

def main():
    """Função principal para testar a ponte"""
    ponte = PonteComunicacao()
    
    print("=== Ponte de Comunicação com Agente Gema ===")
    print("1. Certifique-se de que o agente Gema está rodando")
    print("2. Ative a ponte no agente Gema digitando 'ativar ponte'")
    print("3. Pressione Enter para conectar...")
    input()
    
    if ponte.conectar():
        print("\n🎉 Conexão estabelecida!")
        print("Digite suas mensagens (ou 'sair' para encerrar):")
        
        while True:
            try:
                mensagem = input("\nVocê: ")
                if mensagem.lower() in ['sair', 'quit', 'exit']:
                    break
                
                if mensagem.strip():
                    ponte.enviar_mensagem(mensagem)
                    time.sleep(0.1)  # Pequena pausa para receber resposta
                    
            except KeyboardInterrupt:
                break
    
    ponte.desconectar()
    print("👋 Encerrando ponte de comunicação...")

if __name__ == "__main__":
    main()
