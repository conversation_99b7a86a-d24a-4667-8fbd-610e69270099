@echo off
echo ========================================
echo     REPARACAO WINDOWS EXPLORER
echo ========================================
echo.

REM Verifica se esta executando como administrador
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERRO: Execute este script como ADMINISTRADOR
    pause
    exit /b 1
)

echo ✅ Executando como administrador
echo.

echo 🔍 Diagnosticando Windows Explorer...
echo.

REM Mata processos do Explorer
echo 🛑 Finalizando processos do Explorer...
taskkill /f /im explorer.exe 2>nul
taskkill /f /im dwm.exe 2>nul

echo.
echo 🔧 Reparando componentes do Explorer...

REM Registra novamente as DLLs do Explorer
echo Registrando DLLs do Explorer...
regsvr32 /s shell32.dll
regsvr32 /s ole32.dll
regsvr32 /s oleaut32.dll
regsvr32 /s actxprxy.dll
regsvr32 /s mshtml.dll
regsvr32 /s urlmon.dll
regsvr32 /s browseui.dll
regsvr32 /s shdocvw.dll
regsvr32 /s comctl32.dll
regsvr32 /s user32.dll
regsvr32 /s kernel32.dll
regsvr32 /s ntdll.dll

echo.
echo 🔄 Reparando registro do Explorer...

REM Repara chaves do registro do Explorer
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" /v Shell /t REG_SZ /d "explorer.exe" /f
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Image File Execution Options\explorer.exe" /v Debugger /t REG_SZ /d "" /f 2>nul
reg delete "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Image File Execution Options\explorer.exe" /v Debugger /f 2>nul

echo.
echo 🧹 Limpando cache do Explorer...

REM Limpa cache e arquivos temporarios
del /q /s "%localappdata%\Microsoft\Windows\Explorer\*.db" 2>nul
del /q /s "%appdata%\Microsoft\Windows\Recent\*.*" 2>nul
del /q /s "%temp%\*.*" 2>nul

echo.
echo 🔍 Verificando integridade dos arquivos do sistema...
sfc /scannow

echo.
echo 🚀 Reiniciando Windows Explorer...
start explorer.exe

echo.
echo ⏳ Aguardando inicializacao...
timeout /t 5 /nobreak >nul

echo.
echo 🔍 Verificando se Explorer esta funcionando...
tasklist /fi "imagename eq explorer.exe" | find /i "explorer.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ Windows Explorer reparado e funcionando!
) else (
    echo ❌ Erro: Explorer nao iniciou corretamente
    echo Tentando reiniciar...
    start explorer.exe
)

echo.
echo 🎉 REPARACAO DO EXPLORER CONCLUIDA!
echo.
echo Se ainda houver problemas:
echo 1. Reinicie o computador
echo 2. Execute: DISM /Online /Cleanup-Image /RestoreHealth
echo 3. Execute novamente: sfc /scannow
echo.
pause
