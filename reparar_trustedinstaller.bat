@echo off
echo ========================================
echo    REPARACAO TRUSTEDINSTALLER
echo ========================================
echo.

REM Verifica se esta executando como administrador
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERRO: Execute este script como ADMINISTRADOR
    pause
    exit /b 1
)

echo ✅ Executando como administrador
echo.

echo 🔍 Verificando TrustedInstaller atual...
echo.

REM Verifica o servico TrustedInstaller
sc query TrustedInstaller

echo.
echo 🛑 Parando TrustedInstaller...
net stop TrustedInstaller
sc stop TrustedInstaller

echo.
echo 🔧 Reparando TrustedInstaller...

REM Registra novamente as DLLs do TrustedInstaller
regsvr32 /s %windir%\system32\wuapi.dll
regsvr32 /s %windir%\system32\wuaueng.dll
regsvr32 /s %windir%\system32\wuaueng1.dll
regsvr32 /s %windir%\system32\wucltui.dll
regsvr32 /s %windir%\system32\wups.dll
regsvr32 /s %windir%\system32\wups2.dll
regsvr32 /s %windir%\system32\wuweb.dll

echo.
echo 🔄 Reconfigurando servico TrustedInstaller...

REM Reconfigura o servico
sc config TrustedInstaller start= demand
sc config TrustedInstaller type= own
sc config TrustedInstaller error= normal

echo.
echo 🚀 Reiniciando TrustedInstaller...
net start TrustedInstaller
sc start TrustedInstaller

echo.
echo 🔍 Verificando status final...
sc query TrustedInstaller

echo.
if %errorlevel% equ 0 (
    echo ✅ TrustedInstaller reparado com sucesso!
) else (
    echo ❌ Erro na reparacao do TrustedInstaller
    echo Execute: sfc /scannow
)

echo.
pause
