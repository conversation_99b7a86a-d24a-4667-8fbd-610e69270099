@echo off
echo ========================================
echo   VERIFICACAO ASSINATURAS MICROSOFT
echo ========================================
echo.

REM Verifica se esta executando como administrador
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERRO: Execute este script como ADMINISTRADOR
    pause
    exit /b 1
)

echo ✅ Executando como administrador
echo.

echo 🔍 Verificando assinaturas dos arquivos criticos...
echo.

REM Lista de arquivos criticos para verificar
set arquivos_criticos=^
    %windir%\system32\explorer.exe ^
    %windir%\system32\cmd.exe ^
    %windir%\system32\powershell.exe ^
    %windir%\system32\svchost.exe ^
    %windir%\system32\winlogon.exe ^
    %windir%\system32\csrss.exe ^
    %windir%\system32\lsass.exe ^
    %windir%\system32\services.exe ^
    %windir%\system32\TrustedInstaller.exe

echo Verificando assinaturas digitais...
echo.

for %%f in (%arquivos_criticos%) do (
    if exist "%%f" (
        echo Verificando: %%f
        powershell -Command "Get-AuthenticodeSignature '%%f' | Select-Object Path, Status, SignerCertificate"
        echo.
    ) else (
        echo ❌ ARQUIVO NAO ENCONTRADO: %%f
        echo.
    )
)

echo ========================================
echo VERIFICACAO DE CERTIFICADOS
echo ========================================
echo.

echo 🔍 Verificando certificados da Microsoft...
certlm.msc /s

echo.
echo 🔄 Atualizando lista de certificados revogados...
certutil -urlcache * delete

echo.
echo 📝 Atualizando definicoes do Windows Defender...
"%ProgramFiles%\Windows Defender\MpCmdRun.exe" -SignatureUpdate

echo.
echo ========================================
echo VERIFICACAO CONCLUIDA
echo ========================================
echo.
echo Se algum arquivo mostrou status diferente de "Valid":
echo 1. Execute: sfc /scannow
echo 2. Execute: DISM /Online /Cleanup-Image /RestoreHealth  
echo 3. Considere reinstalar o Windows se muitos arquivos estao comprometidos
echo.
pause
