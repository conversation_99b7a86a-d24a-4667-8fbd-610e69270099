# Script para instalar Python 3.13 em C:\python313
Write-Host "🐍 INSTALADOR PYTHON 3.13 PARA AGENTE GEMA" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

$pythonDir = "C:\python313"
$installerUrl = "https://www.python.org/ftp/python/3.13.0/python-3.13.0-amd64.exe"
$installerPath = "python-installer.exe"

# Verifica se Python já está instalado
if (Test-Path "$pythonDir\python.exe") {
    Write-Host "✅ Python já está instalado em $pythonDir" -ForegroundColor Green
} else {
    Write-Host "📥 Baixando Python 3.13..." -ForegroundColor Yellow
    
    try {
        Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath -UseBasicParsing
        Write-Host "✅ Download concluído!" -ForegroundColor Green
        
        Write-Host "🔧 Instalando Python em $pythonDir..." -ForegroundColor Yellow
        
        # Instala Python silenciosamente
        $installArgs = @(
            "/quiet",
            "InstallAllUsers=0",
            "TargetDir=$pythonDir",
            "PrependPath=1",
            "Include_test=0",
            "Include_pip=1",
            "Include_tcltk=1",
            "Include_launcher=1"
        )
        
        Start-Process -FilePath $installerPath -ArgumentList $installArgs -Wait
        
        # Remove o instalador
        Remove-Item $installerPath -Force
        
        Write-Host "✅ Python instalado com sucesso!" -ForegroundColor Green

    } catch {
        Write-Host "❌ Erro no download: $_" -ForegroundColor Red
        exit 1
    }
}

# Verifica a instalação
if (Test-Path "$pythonDir\python.exe") {
    Write-Host "🔍 Verificando instalação..." -ForegroundColor Yellow
    
    # Testa Python
    $version = & "$pythonDir\python.exe" --version 2>&1
    Write-Host "✅ Versão: $version" -ForegroundColor Green
    
    # Atualiza pip
    Write-Host "📦 Atualizando pip..." -ForegroundColor Yellow
    & "$pythonDir\python.exe" -m pip install --upgrade pip
    
    Write-Host "🎉 INSTALAÇÃO CONCLUÍDA!" -ForegroundColor Green
    Write-Host "Python instalado em: $pythonDir" -ForegroundColor Cyan
    Write-Host "Agora você pode executar: $pythonDir\python.exe agente_gema.py" -ForegroundColor Cyan
    
} else {
    Write-Host "❌ Falha na instalação do Python" -ForegroundColor Red
    exit 1
}

Write-Host "`nPressione qualquer tecla para continuar..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
