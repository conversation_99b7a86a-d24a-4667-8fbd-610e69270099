import time
import threading
from ponte_comunicacao import PonteComunicacao

class ConversaAutomatica:
    def __init__(self):
        self.ponte = PonteComunicacao("Agente Augment AI")
        self.conversando = False
        
    def iniciar_conversa(self):
        """Inicia uma conversa automatizada com o agente Gema"""
        print("🤖 Iniciando conversa automatizada com Agente Gema...")
        
        # Tenta conectar
        if not self.ponte.conectar():
            print("❌ Não foi possível conectar. Certifique-se de que:")
            print("1. O agente Gema está rodando (python agente_gema.py)")
            print("2. A ponte foi ativada (digite 'ativar ponte' no agente)")
            return
        
        self.conversando = True
        
        # Aguarda um pouco para estabelecer conexão
        time.sleep(1)
        
        # Sequência de mensagens para testar a comunicação
        mensagens_teste = [
            "Olá Gema! Sou o Agente Augment AI",
            "Como você está hoje?",
            "Você pode me ajudar com algumas informações?",
            "Qual é o seu nome completo?",
            "Obrigado pela conversa!"
        ]
        
        print("\n🎬 Iniciando sequência de teste...")
        
        for i, mensagem in enumerate(mensagens_teste, 1):
            if not self.conversando:
                break
                
            print(f"\n--- Mensagem {i}/{len(mensagens_teste)} ---")
            self.ponte.enviar_mensagem(mensagem)
            
            # Aguarda resposta
            time.sleep(3)
        
        print("\n✅ Sequência de teste concluída!")
        
        # Agora permite conversa livre
        self.conversa_livre()
    
    def conversa_livre(self):
        """Permite conversa livre com o agente"""
        print("\n💬 Agora você pode conversar livremente com a Gema!")
        print("Digite 'sair' para encerrar a conversa.")
        
        while self.conversando:
            try:
                mensagem = input("\nVocê para Gema: ")
                
                if mensagem.lower() in ['sair', 'quit', 'exit', 'tchau']:
                    self.ponte.enviar_mensagem("tchau")
                    time.sleep(1)
                    break
                
                if mensagem.strip():
                    self.ponte.enviar_mensagem(mensagem)
                    
            except KeyboardInterrupt:
                break
        
        self.encerrar_conversa()
    
    def encerrar_conversa(self):
        """Encerra a conversa"""
        self.conversando = False
        self.ponte.desconectar()
        print("\n👋 Conversa encerrada!")

def main():
    print("=" * 50)
    print("🌉 PONTE DE COMUNICAÇÃO AGENTE GEMA")
    print("=" * 50)
    print()
    print("Este script estabelece comunicação direta com seu Agente Gema.")
    print()
    print("INSTRUÇÕES:")
    print("1. Abra outro terminal")
    print("2. Execute: python agente_gema.py")
    print("3. No agente Gema, digite: ativar ponte")
    print("4. Volte aqui e pressione Enter para continuar...")
    print()
    
    input("Pressione Enter quando a ponte estiver ativa...")
    
    conversa = ConversaAutomatica()
    conversa.iniciar_conversa()

if __name__ == "__main__":
    main()
