@echo off
echo ========================================
echo   INSTALADOR BIBLIOTECAS AGENTE GEMA
echo ========================================
echo.

REM Define o caminho do Python
set PYTHON_DIR=C:\python313
set PYTHON_EXE=%PYTHON_DIR%\python.exe

REM Verifica se Python esta instalado
if not exist "%PYTHON_EXE%" (
    echo ❌ Python nao encontrado em %PYTHON_DIR%
    echo Execute primeiro: instalar_python_simples.bat
    pause
    exit /b 1
)

echo ✅ Python encontrado: %PYTHON_EXE%
echo.

REM Verifica versao do Python
echo 🔍 Verificando versao do Python...
"%PYTHON_EXE%" --version
echo.

echo 📦 Atualizando pip...
"%PYTHON_EXE%" -m pip install --upgrade pip
echo.

echo 📚 Instalando bibliotecas para o Agente Gema...
echo.

REM As bibliotecas do Agente Gema (todas sao built-in do Python)
echo ℹ️  Verificando bibliotecas necessarias:
echo   - sys (built-in)
echo   - time (built-in) 
echo   - json (built-in)
echo   - socket (built-in)
echo   - threading (built-in)
echo   - datetime (built-in)
echo.

echo ✅ Todas as bibliotecas necessarias ja estao incluidas no Python!
echo.

REM Instala algumas bibliotecas extras uteis
echo 📦 Instalando bibliotecas extras uteis...
"%PYTHON_EXE%" -m pip install requests
"%PYTHON_EXE%" -m pip install colorama

echo.
echo 🧪 Testando instalacao...
echo.

REM Testa se consegue importar as bibliotecas
"%PYTHON_EXE%" -c "import sys, time, json, socket, threading; from datetime import datetime; print('✅ Todas as bibliotecas importadas com sucesso!')"

if %errorlevel% equ 0 (
    echo.
    echo 🎉 INSTALACAO CONCLUIDA COM SUCESSO!
    echo.
    echo ✅ Python instalado em: %PYTHON_DIR%
    echo ✅ Todas as bibliotecas necessarias estao disponiveis
    echo.
    echo 🚀 Agora voce pode executar o Agente Gema com:
    echo    %PYTHON_EXE% agente_gema.py
    echo.
    echo 💡 Ou use o script: executar_gema.bat
    echo.
) else (
    echo ❌ Erro ao testar as bibliotecas
    echo Verifique a instalacao do Python
)

echo.
pause
