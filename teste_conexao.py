#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# Adiciona o diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from agente_gema import AgenteGema
    
    print("=" * 60)
    print("🤖 TESTE DE CONEXÃO COM AGENTE GEMA")
    print("=" * 60)
    
    # Cria o agente
    gema = AgenteGema()
    print(f"✅ Agente {gema.nome} inicializado com sucesso!")
    
    # Testa algumas interações
    mensagens_teste = [
        "olá",
        "como você está?",
        "ajuda",
        "ativar ponte",
        "status ponte"
    ]
    
    print("\n🧪 Executando testes de comunicação:")
    print("-" * 40)
    
    for i, msg in enumerate(mensagens_teste, 1):
        print(f"\n[Teste {i}] Você: {msg}")
        resposta = gema.responder(msg)
        print(f"[Teste {i}] Gema: {resposta}")
    
    print("\n" + "=" * 60)
    print("✅ TODOS OS TESTES CONCLUÍDOS COM SUCESSO!")
    print("🎉 Seu Agente Gema está funcionando perfeitamente!")
    print("=" * 60)
    
    # Agora inicia conversa interativa
    print("\n💬 Agora você pode conversar diretamente:")
    print("Digite 'sair' para encerrar a conversa")
    print("-" * 40)
    
    while True:
        try:
            mensagem = input("\n💬 Você: ")
            
            if mensagem.lower() in ['sair', 'exit', 'quit']:
                print("🤖 Gema: Até logo! Foi um prazer conversar!")
                break
                
            if mensagem.strip():
                resposta = gema.responder(mensagem)
                print(f"🤖 Gema: {resposta}")
                
        except KeyboardInterrupt:
            print("\n🤖 Gema: Até logo! Foi um prazer conversar!")
            break
        except EOFError:
            print("\n🤖 Gema: Até logo! Foi um prazer conversar!")
            break

except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    print("Verifique se o arquivo agente_gema.py está no diretório correto.")
except Exception as e:
    print(f"❌ Erro inesperado: {e}")
    import traceback
    traceback.print_exc()

print("\n👋 Conexão encerrada!")
