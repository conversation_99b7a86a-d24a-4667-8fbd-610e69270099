@echo off
echo ========================================
echo    INSTALACAO COMPLETA AGENTE GEMA
echo ========================================
echo.
echo Este script ira:
echo 1. Instalar Python 3.12.7 em C:\python313
echo 2. Instalar todas as bibliotecas necessarias
echo 3. Testar a instalacao
echo 4. Executar o Agente Gema
echo.
echo Pressione qualquer tecla para continuar...
pause >nul
echo.

echo ========================================
echo ETAPA 1: INSTALANDO PYTHON
echo ========================================
call instalar_python_simples.bat

echo.
echo ========================================
echo ETAPA 2: INSTALANDO BIBLIOTECAS
echo ========================================
call instalar_bibliotecas.bat

echo.
echo ========================================
echo ETAPA 3: EXECUTANDO AGENTE GEMA
echo ========================================
echo.
echo Deseja executar o Agente Gema agora? (S/N)
set /p resposta=
if /i "%resposta%"=="S" (
    call executar_gema.bat
) else (
    echo.
    echo ✅ Instalacao concluida!
    echo Para executar o Agente Gema, use: executar_gema.bat
)

echo.
pause
